import * as React from "react";
import {
  RouterProvider,
  create<PERSON>rowser<PERSON><PERSON><PERSON>,
  j<PERSON>,
  <PERSON>,
  RouteObject,
  useLocation,
  useRouteError,
  isRouteErrorResponse,
} from "react-router-dom";
import { StaticRouterProvider } from "react-router-dom/server";

import { getMcpServers, getNewMcpServer, getEditMcpServer } from "$app/data/mcp_servers";
import { assertDefined } from "$app/utils/assert";
import { buildStaticRouter, GlobalProps, register } from "$app/utils/serverComponentUtil";

import { Icon } from "$app/components/Icons";
import McpServerForm from "$app/components/server-components/McpServersPage/McpServerForm";
import McpServerList from "$app/components/server-components/McpServersPage/McpServerList";
import McpServerDetail from "$app/components/server-components/McpServersPage/McpServerDetail";

type LayoutProps = {
  title: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
};

export const Layout = ({ title, actions, children }: LayoutProps) => {
  return (
    <main>
      <header>
        <h1>{title}</h1>
        {actions ? <div className="actions">{actions}</div> : null}
      </header>
      {children}
    </main>
  );
};

export const NewMcpServerButton = () => {
  const { pathname: from } = useLocation();
  return (
    <Link className="button accent" to="/mcp_servers/new" state={{ from }}>
      <Icon name="plus" />
      New MCP Server
    </Link>
  );
};

export const EditMcpServerButton = ({ id }: { id: string }) => {
  const { pathname: from } = useLocation();
  return (
    <Link className="button" to={`/mcp_servers/${id}/edit`} state={{ from }}>
      <Icon name="pencil" />
      Edit
    </Link>
  );
};

export const McpServerHealthBadge = ({ status }: { status: "healthy" | "unhealthy" | "unknown" }) => {
  const statusConfig = {
    healthy: { color: "success", icon: "solid-check-circle" as const, text: "Healthy" },
    unhealthy: { color: "danger", icon: "x-circle-fill" as const, text: "Unhealthy" },
    unknown: { color: "warning", icon: "outline-clock" as const, text: "Unknown" },
  };

  const config = statusConfig[status];

  return (
    <span className={`badge ${config.color}`}>
      <Icon name={config.icon} />
      {config.text}
    </span>
  );
};

export const EmptyStatePlaceholder = () => (
  <div className="placeholder">
    <figure>
      <Icon name="hdd-network-fill" />
    </figure>
    <h2>No MCP servers yet</h2>
    <p>Create your first MCP server to start monetizing your AI tools and services.</p>
    <NewMcpServerButton />
    <p>
      <a data-helper-prompt="How do I create an MCP server?">Learn more about MCP servers</a>
    </p>
  </div>
);

const ErrorBoundary = () => {
  const error = useRouteError();
  return (
    <main>
      <div>
        <div className="placeholder">
          <p>
            {isRouteErrorResponse(error) && error.status === 404
              ? "The MCP server you're looking for doesn't exist."
              : "Something went wrong."}
          </p>
        </div>
      </div>
    </main>
  );
};

const routes: RouteObject[] = [
  {
    path: "/mcp_servers",
    element: <McpServerList />,
    loader: async () => {
      try {
        return json(await getMcpServers(), { status: 200 });
      } catch (error) {
        // During SSR or when authentication fails, provide fallback data
        return json(
          { mcp_servers: [], pagination: { current_page: 1, total_pages: 1, total_count: 0 } },
          { status: 200 },
        );
      }
    },
  },
  {
    path: "/mcp_servers/new",
    element: <McpServerForm />,
    loader: async () => {
      try {
        return json(await getNewMcpServer(), { status: 200 });
      } catch (error) {
        // During SSR or when authentication fails, provide fallback data
        return json({ mcp_server: null, errors: [] }, { status: 200 });
      }
    },
  },
  {
    path: "/mcp_servers/:id",
    element: <McpServerDetail />,
    errorElement: <ErrorBoundary />,
    loader: async ({ params }) => {
      try {
        const response = await getEditMcpServer(assertDefined(params.id, "MCP Server ID is required"));
        return response.success ? json(response) : new Response("", { status: response.status || 404 });
      } catch (error) {
        // During SSR or when authentication fails, return 404
        return new Response("", { status: 404 });
      }
    },
  },
  {
    path: "/mcp_servers/:id/edit",
    element: <McpServerForm />,
    errorElement: <ErrorBoundary />,
    loader: async ({ params }) => {
      try {
        const response = await getEditMcpServer(assertDefined(params.id, "MCP Server ID is required"));
        return response.success ? json(response) : new Response("", { status: response.status || 404 });
      } catch (error) {
        // During SSR or when authentication fails, provide fallback data
        return json({ mcp_server: null, errors: [] }, { status: 200 });
      }
    },
  },
];

const McpServersPage = () => {
  const router = createBrowserRouter(routes);

  return <RouterProvider router={router} />;
};

const McpServersRouter = async (global: GlobalProps) => {
  const { router, context } = await buildStaticRouter(global, routes);
  const component = () => <StaticRouterProvider router={router} context={context} nonce={global.csp_nonce} />;
  component.displayName = "McpServersRouter";
  return component;
};

export default register({ component: McpServersPage, ssrComponent: McpServersRouter, propParser: () => ({}) });
